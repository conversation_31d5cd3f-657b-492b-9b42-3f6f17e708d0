<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

/**
 * Middleware de test pour démontrer le fonctionnement des middlewares
 *
 * EXPLICATION DES MIDDLEWARES :
 *
 * Un middleware est une couche intermédiaire qui s'exécute entre la requête HTTP
 * et la réponse. Il permet de :
 * - Filtrer les requêtes avant qu'elles atteignent le contrôleur
 * - Modifier les réponses avant qu'elles soient envoyées au client
 * - Effectuer des vérifications (authentification, autorisation, etc.)
 * - Logger les requêtes
 * - Gérer les CORS, la sécurité, etc.
 */
class TestMiddleware
{
    /**
     * Handle an incoming request.
     *
     * FONCTIONNEMENT :
     * 1. Cette méthode est appelée pour chaque requête qui passe par ce middleware
     * 2. $request contient toutes les données de la requête HTTP
     * 3. $next est une fonction qui passe la requête au middleware suivant ou au contrôleur
     * 4. On peut exécuter du code AVANT $next($request) (pré-traitement)
     * 5. On peut exécuter du code APRÈS $next($request) (post-traitement)
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // ========================================
        // CODE EXÉCUTÉ AVANT LA REQUÊTE (PRÉ-TRAITEMENT)
        // ========================================

        // 1. Logger les informations de la requête
        Log::info('🔍 TestMiddleware - Requête entrante', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->format('Y-m-d H:i:s')
        ]);

        // 2. Vérification personnalisée (exemple : bloquer certaines IPs)
        $blockedIps = ['*************', '*********']; // IPs bloquées pour l'exemple
        if (in_array($request->ip(), $blockedIps)) {
            Log::warning('🚫 IP bloquée tentative d\'accès', ['ip' => $request->ip()]);
            return response()->json(['error' => 'Accès refusé'], 403);
        }

        // 3. Ajouter des headers personnalisés à la requête
        $request->headers->set('X-Test-Middleware', 'Processed');
        $request->headers->set('X-Request-Time', now()->timestamp);

        // 4. Vérification du User-Agent (exemple : bloquer les bots)
        $userAgent = $request->userAgent();
        if (str_contains(strtolower($userAgent), 'bot') && !str_contains(strtolower($userAgent), 'googlebot')) {
            Log::info('🤖 Bot détecté et bloqué', ['user_agent' => $userAgent]);
            return response('Accès refusé aux bots', 403);
        }

        // 5. Mesurer le temps de traitement (début)
        $startTime = microtime(true);

        // ========================================
        // PASSAGE AU MIDDLEWARE SUIVANT OU AU CONTRÔLEUR
        // ========================================
        $response = $next($request);

        // ========================================
        // CODE EXÉCUTÉ APRÈS LA RÉPONSE (POST-TRAITEMENT)
        // ========================================

        // 6. Calculer le temps de traitement
        $endTime = microtime(true);
        $processingTime = round(($endTime - $startTime) * 1000, 2); // en millisecondes

        // 7. Logger les informations de la réponse
        Log::info('📤 TestMiddleware - Réponse sortante', [
            'status_code' => $response->getStatusCode(),
            'processing_time_ms' => $processingTime,
            'content_type' => $response->headers->get('Content-Type'),
            'content_length' => strlen($response->getContent())
        ]);

        // 8. Ajouter des headers personnalisés à la réponse
        $response->headers->set('X-Processing-Time', $processingTime . 'ms');
        $response->headers->set('X-Processed-By', 'TestMiddleware');
        $response->headers->set('X-Server-Time', now()->toISOString());

        // 9. Modifier le contenu de la réponse si nécessaire (exemple pour les vues HTML)
        if ($response->headers->get('Content-Type') && str_contains($response->headers->get('Content-Type'), 'text/html')) {
            $content = $response->getContent();

            // Ajouter un commentaire HTML avec les informations de debug
            $debugInfo = "<!-- \n";
            $debugInfo .= "🔧 Traité par TestMiddleware\n";
            $debugInfo .= "⏱️  Temps de traitement: {$processingTime}ms\n";
            $debugInfo .= "🕐 Heure du serveur: " . now()->format('Y-m-d H:i:s') . "\n";
            $debugInfo .= "🌐 IP du client: " . $request->ip() . "\n";
            $debugInfo .= "-->\n";

            $content = str_replace('</body>', $debugInfo . '</body>', $content);
            $response->setContent($content);
        }

        // 10. Retourner la réponse modifiée
        return $response;
    }
}
