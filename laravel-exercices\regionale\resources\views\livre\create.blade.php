
@extends("layout")

@section("content")

<div class="container mt-5">
    <h1 class="mb-4">Ajouter un livre</h1>

    <form action="{{ route('livre.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="mb-3">
            <label for="titre" class="form-label">Titre</label>
            <input type="text" name="titre" id="titre" class="form-control" value="{{ old('titre') }}">
            @error('titre')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
    <label for="auteur_id" class="form-label">Auteur</label>
    <select name="auteur_id" id="auteur_id" class="form-control">
        <option value="">-- Choisir un auteur --</option>
        @foreach($auteurs as $auteur)
            <option value="{{ $auteur->id }}" {{ old('auteur_id') == $auteur->id ? 'selected' : '' }}>
                {{ $auteur->nom }} {{ $auteur->prenom }}
            </option>
        @endforeach
    </select>
    @error('auteur_id')
        <div class="text-danger">{{ $message }}</div>
    @enderror
</div>


        <div class="mb-3">
            <label for="image" class="form-label">Image</label>
            <input type="file" name="image" id="image" class="form-control">
            @error('image')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" class="btn btn-success">Ajouter</button>
    </form>
</div>

@endsection
