<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Livres - Application avec Authentification</title>

        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous">
        </script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    </head>

    <body>
        {{-- Navigation avec authentification --}}
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="{{ route('home') }}">
                    📚 Bibliothèque
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        @auth
                            {{-- Liens pour les utilisateurs connectés --}}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('livre.index') }}">
                                    📖 Livres
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('test.middleware') }}">
                                    🧪 Test Middleware
                                </a>
                            </li>
                        @endauth
                    </ul>

                    <ul class="navbar-nav">
                        @guest
                            {{-- Liens pour les utilisateurs non connectés --}}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">
                                    🔐 Connexion
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('register') }}">
                                    ✨ Inscription
                                </a>
                            </li>
                        @else
                            {{-- Menu pour les utilisateurs connectés --}}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    👤 {{ Auth::user()->name }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <span class="dropdown-item-text">
                                            <small class="text-muted">{{ Auth::user()->email }}</small>
                                        </span>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit" class="dropdown-item">
                                                🚪 Déconnexion
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        {{-- Contenu principal --}}
        <main>
            @yield("content")
        </main>

        {{-- Footer avec informations sur l'authentification --}}
        <footer class="bg-light mt-5 py-3">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            📚 Application de gestion de livres avec authentification
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        @auth
                            <small class="text-muted">
                                Connecté en tant que <strong>{{ Auth::user()->name }}</strong>
                            </small>
                        @else
                            <small class="text-muted">
                                <a href="{{ route('login') }}">Se connecter</a> pour accéder à l'application
                            </small>
                        @endauth
                    </div>
                </div>
            </div>
        </footer>
    </body>

</html>
