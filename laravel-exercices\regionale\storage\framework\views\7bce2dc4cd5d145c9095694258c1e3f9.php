


<?php $__env->startSection("content"); ?>
<div class="container mt-4">
    <h1 class="mb-4">Liste des livres</h1>

    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="<?php echo e(route('livre.create')); ?>" class="btn btn-primary">Ajouter un livre</a>
    </div>
    <?php if(session('success')): ?>
        <div class="alert alert-success">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>
    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>Image</th>
                <th>Titre</th>
                <th>Auteur</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $livres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $livre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td>
                        <?php if($livre->image && file_exists(public_path('storage/' . $livre->image))): ?>
                            <img src="<?php echo e(asset('storage/' . $livre->image)); ?>"
                                 alt="Image du livre <?php echo e($livre->titre); ?>"
                                 width="100"
                                 height="120"
                                 class="img-thumbnail"
                                 style="object-fit: cover;"
                                 onerror="this.src='<?php echo e(asset('images/default-book.svg')); ?>'">
                        <?php else: ?>
                            <img src="<?php echo e(asset('images/default-book.svg')); ?>"
                                 alt="Image par défaut"
                                 width="100"
                                 height="120"
                                 class="img-thumbnail">
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($livre->titre); ?></td>
                    <td><?php echo e($livre->auteur->nom ?? 'N/A'); ?> <?php echo e($livre->auteur->prenom ?? ''); ?></td>
                    <td>
                        <a href="<?php echo e(route('livre.show', $livre->id)); ?>" class="btn btn-info btn-sm">Afficher</a>
                        <a href="<?php echo e(route('livre.edit', $livre->id)); ?>" class="btn btn-warning btn-sm">Modifier</a>

                       <form action="<?php echo e(route('livre.destroy', $livre->id)); ?>" method="post" onsubmit="return confirm('Voulez-vous vraiment supprimer ce livre ?')" style="display:inline-block;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger btn-sm">Supprimer</button>
                            </form>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="4" class="text-center">Aucun livre trouvé.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Liens de pagination -->
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($livres->links()); ?>

    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/livre/index.blade.php ENDPATH**/ ?>