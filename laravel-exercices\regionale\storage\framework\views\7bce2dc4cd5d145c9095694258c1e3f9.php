

<?php $__env->startSection("content"); ?>
<div class="container mt-4">
    <h1 class="mb-4">Liste des livres</h1>

    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="<?php echo e(route('livre.create')); ?>" class="btn btn-primary">Ajouter un livre</a>

        <div class="d-flex align-items-center gap-3">
            <!-- Sélecteur du nombre d'éléments par page -->
            <div class="d-flex align-items-center">
                <label for="per_page" class="form-label me-2 mb-0">Afficher :</label>
                <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                    <option value="5" <?php echo e(request('per_page', 5) == 5 ? 'selected' : ''); ?>>5</option>
                    <option value="10" <?php echo e(request('per_page') == 10 ? 'selected' : ''); ?>>10</option>
                    <option value="15" <?php echo e(request('per_page') == 15 ? 'selected' : ''); ?>>15</option>
                    <option value="20" <?php echo e(request('per_page') == 20 ? 'selected' : ''); ?>>20</option>
                </select>
                <span class="ms-2">par page</span>
            </div>

            <div class="text-muted">
                <small>
                    Affichage de <?php echo e($livres->firstItem() ?? 0); ?> à <?php echo e($livres->lastItem() ?? 0); ?>

                    sur <?php echo e($livres->total()); ?> livres
                </small>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>


    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>Image</th>
                <th>Titre</th>
                <th>Auteur</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $livres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $livre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><img src="<?php echo e(asset('storage/' . $livre->image)); ?>" alt="Image du livre" width="100"></td>
                    <td><?php echo e($livre->titre); ?></td>
                    <td><?php echo e($livre->auteur->nom ?? 'N/A'); ?> <?php echo e($livre->auteur->prenom ?? ''); ?></td>
                    <td>
                        <a href="<?php echo e(route('livre.show', $livre->id)); ?>" class="btn btn-info btn-sm">Afficher</a>
                        <a href="<?php echo e(route('livre.edit', $livre->id)); ?>" class="btn btn-warning btn-sm">Modifier</a>

                       <form action="<?php echo e(route('livre.destroy', $livre->id)); ?>" method="post" onsubmit="return confirm('Voulez-vous vraiment supprimer ce livre ?')" style="display:inline-block;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger btn-sm">Supprimer</button>
                            </form>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="4" class="text-center">Aucun livre trouvé.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Liens de pagination -->
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($livres->links()); ?>

    </div>
</div>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Retourner à la première page
    window.location.href = url.toString();
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/livre/index.blade.php ENDPATH**/ ?>