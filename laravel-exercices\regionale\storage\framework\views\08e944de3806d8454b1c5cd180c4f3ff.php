<?php $__env->startSection("content"); ?>
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">🔐 Connexion</h4>
                </div>
                <div class="card-body">
                    
                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    
                    <div class="alert alert-info">
                        <strong>ℹ️ Comment fonctionne l'authentification :</strong><br>
                        1. Saisissez votre email et mot de passe<br>
                        2. Laravel vérifie vos identifiants dans la base de données<br>
                        3. Si correct, une session est créée pour vous identifier<br>
                        4. Vous êtes redirigé vers l'application protégée
                    </div>

                    <form method="POST" action="<?php echo e(route('login')); ?>">
                        <?php echo csrf_field(); ?>

                        
                        <div class="mb-3">
                            <label for="email" class="form-label">📧 Email</label>
                            <input type="email" 
                                   name="email" 
                                   id="email" 
                                   class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   value="<?php echo e(old('email')); ?>" 
                                   placeholder="<EMAIL>"
                                   required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        
                        <div class="mb-3">
                            <label for="password" class="form-label">🔑 Mot de passe</label>
                            <input type="password" 
                                   name="password" 
                                   id="password" 
                                   class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   placeholder="Votre mot de passe"
                                   required>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" name="remember" id="remember" class="form-check-input">
                            <label class="form-check-label" for="remember">
                                💾 Se souvenir de moi
                            </label>
                            <small class="form-text text-muted d-block">
                                Garde votre session active plus longtemps
                            </small>
                        </div>

                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                🚀 Se connecter
                            </button>
                        </div>
                    </form>

                    
                    <div class="text-center mt-3">
                        <p class="mb-0">Pas encore de compte ?</p>
                        <a href="<?php echo e(route('register')); ?>" class="btn btn-link">
                            ✨ Créer un compte
                        </a>
                    </div>

                    
                    <div class="alert alert-warning mt-3">
                        <strong>🧪 Compte de test :</strong><br>
                        Email: <code><EMAIL></code><br>
                        Mot de passe: <code>password</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!--
🔧 EXPLICATION TECHNIQUE DE LA CONNEXION :

1. FORMULAIRE :
   - method="POST" : Envoie les données de manière sécurisée
   - <?php echo csrf_field(); ?> : Token de protection contre les attaques CSRF
   - action="<?php echo e(route('login')); ?>" : Envoie vers AuthController@login

2. VALIDATION :
   - <?php $__errorArgs = ['field'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> : Affiche les erreurs de validation
   - old('field') : Conserve les valeurs en cas d'erreur
   - is-invalid : Classe Bootstrap pour les champs en erreur

3. SÉCURITÉ :
   - type="password" : Masque la saisie
   - Hash::make() : Hache le mot de passe en base
   - Auth::attempt() : Vérifie les identifiants de manière sécurisée

4. SESSION :
   - session()->regenerate() : Régénère l'ID de session
   - remember : Cookie de longue durée
   - Auth::check() : Vérifie si l'utilisateur est connecté
-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/auth/login.blade.php ENDPATH**/ ?>