

<?php $__env->startSection("content"); ?>
<div class="container mt-5">
    <h1 class="mb-4">Détail du livre</h1>

    <div class="card" style="width: 18rem;">
        
        <img 
            src="<?php echo e($livre->image && file_exists(public_path('storage/' . $livre->image)) 
                        ? asset('storage/' . $livre->image) 
                        : asset('images/default-book.svg')); ?>" 
            class="card-img-top"
            alt="Image du livre"
            style="height: 250px; object-fit: cover;"
            onerror="this.src='<?php echo e(asset('images/default-book.svg')); ?>'">

        <div class="card-body">
            <h5 class="card-title"><?php echo e($livre->titre); ?></h5>
            <p class="card-text">
                <strong>Auteur :</strong> <?php echo e($livre->auteur->nom); ?> <?php echo e($livre->auteur->prenom); ?>

            </p>
            <a href="<?php echo e(route('livre.index')); ?>" class="btn btn-secondary">Retour</a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/livre/show.blade.php ENDPATH**/ ?>