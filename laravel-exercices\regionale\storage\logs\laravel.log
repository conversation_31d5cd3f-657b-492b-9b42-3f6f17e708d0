[2025-05-24 14:05:33] local.ERROR: Command "make:middelware" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:middelware\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
[2025-05-24 14:22:44] local.ERROR: Command "make:migartion" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:migartion\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
[2025-05-24 14:59:21] local.ERROR: Command "seed" is not defined.

Did you mean one of these?
    db:seed
    make:seeder {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"seed\" is not defined.

Did you mean one of these?
    db:seed
    make:seeder at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
[2025-05-24 14:59:35] local.ERROR: Call to undefined function Database\Seeders\table() {"exception":"[object] (Error(code: 0): Call to undefined function Database\\Seeders\\table() at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\LivreSeeder.php:17)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\LivreSeeder->run()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\DatabaseSeeder.php(23): Illuminate\\Database\\Seeder->call()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#33 {main}
"} 
[2025-05-24 15:00:02] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' (Connection: mysql, SQL: insert into `users` (`name`, `email`, `email_verified_at`, `password`, `remember_token`, `updated_at`, `created_at`) values (Test User, <EMAIL>, 2025-05-24 15:00:01, $2y$12$NAcTNaNCtzrx5MvQjBLgfu02oNp2ZFoT/wDoo8TJBLd1bvtXdBaOK, luTCQVkG5r, 2025-05-24 15:00:02, 2025-05-24 15:00:02)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' (Connection: mysql, SQL: insert into `users` (`name`, `email`, `email_verified_at`, `password`, `remember_token`, `updated_at`, `created_at`) values (Test User, <EMAIL>, 2025-05-24 15:00:01, $2y$12$NAcTNaNCtzrx5MvQjBLgfu02oNp2ZFoT/wDoo8TJBLd1bvtXdBaOK, luTCQVkG5r, 2025-05-24 15:00:02, 2025-05-24 15:00:02)) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:817)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(351): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Eloquent\\Factories\\Factory->Illuminate\\Database\\Eloquent\\Factories\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(346): Illuminate\\Support\\Collection->each()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(302): Illuminate\\Database\\Eloquent\\Factories\\Factory->store()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(296): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\DatabaseSeeder.php(18): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#38 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(351): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Eloquent\\Factories\\Factory->Illuminate\\Database\\Eloquent\\Factories\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(346): Illuminate\\Support\\Collection->each()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(302): Illuminate\\Database\\Eloquent\\Factories\\Factory->store()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\Factory.php(296): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\DatabaseSeeder.php(18): Illuminate\\Database\\Eloquent\\Factories\\Factory->create()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#40 {main}
"} 
[2025-05-24 15:08:04] local.ERROR: Call to undefined method App\Models\Auteur::factory() {"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Auteur::factory() at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\AuteurSeeder.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\AuteurSeeder->run()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\DatabaseSeeder.php(23): Illuminate\\Database\\Seeder->call()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#37 {main}
"} 
[2025-05-24 15:11:43] local.ERROR: Call to undefined method App\Models\Auteur::factory() {"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Auteur::factory() at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\AuteurSeeder.php(34): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\AuteurSeeder->run()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\database\\seeders\\DatabaseSeeder.php(23): Illuminate\\Database\\Seeder->call()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#37 {main}
"} 
[2025-05-24 15:25:12] local.ERROR: The "--rousource" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--rousource\" option does not exist. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#12 {main}
"} 
[2025-05-24 15:25:22] local.ERROR: The "--rosource" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--rosource\" option does not exist. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#12 {main}
"} 
[2025-05-24 15:25:31] local.ERROR: The "--ressource" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--ressource\" option does not exist. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#12 {main}
"} 
[2025-05-24 20:15:55] local.ERROR: Route [livres.create] not defined. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\Livres\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Route [livres.create] not defined. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\Livres\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [livres.create] not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(856): Illuminate\\Routing\\UrlGenerator->route()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\1c2c3db67d6f51fdf0226413fa6697ae.php(7): route()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:17:14] local.ERROR: Route [livres.create] not defined. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\Livres\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Route [livres.create] not defined. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\Livres\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [livres.create] not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(856): Illuminate\\Routing\\UrlGenerator->route()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\5000b6fe7b623aa7f69eee5ec60a964f.php(7): route()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:18:12] local.ERROR: Route [livres.create] not defined. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livres\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Route [livres.create] not defined. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livres\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [livres.create] not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(856): Illuminate\\Routing\\UrlGenerator->route()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\1c2c3db67d6f51fdf0226413fa6697ae.php(7): route()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:19:01] local.ERROR: Route [livres.create] not defined. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livres\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Route [livres.create] not defined. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livres\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [livres.create] not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(856): Illuminate\\Routing\\UrlGenerator->route()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\1c2c3db67d6f51fdf0226413fa6697ae.php(7): route()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:22:08] local.ERROR: Route [Livre.show] not defined. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livre\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Route [Livre.show] not defined. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livre\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [Livre.show] not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(856): Illuminate\\Routing\\UrlGenerator->route()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php(28): route()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:23:08] local.ERROR: View [content] not found. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livre\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): View [content] not found. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livre\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): View [content] not found. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php(44): Illuminate\\View\\Factory->make()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
[2025-05-24 20:23:50] local.ERROR: View [content] not found. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livre\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): View [content] not found. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livre\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): View [content] not found. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php(44): Illuminate\\View\\Factory->make()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
[2025-05-24 20:23:55] local.ERROR: View [content] not found. (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livre\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): View [content] not found. (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livre\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): View [content] not found. at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php(44): Illuminate\\View\\Factory->make()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#59 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#60 {main}
"} 
[2025-05-24 20:38:44] local.ERROR: Undefined variable $annonce (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\livre\index.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Undefined variable $annonce (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\livre\\index.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php:26)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#55 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $annonce at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php:26)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\7bce2dc4cd5d145c9095694258c1e3f9.php(26): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-05-24 20:55:04] local.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'auteur_id' doesn't have a default value (Connection: mysql, SQL: insert into `livres` (`titre`, `image`, `updated_at`, `created_at`) values (test 2, image/z1ik835YAN8FL0Nh8KNARPSj1cnHEtSnQWyG82R1.jpg, 2025-05-24 20:55:03, 2025-05-24 20:55:03)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'auteur_id' doesn't have a default value (Connection: mysql, SQL: insert into `livres` (`titre`, `image`, `updated_at`, `created_at`) values (test 2, image/z1ik835YAN8FL0Nh8KNARPSj1cnHEtSnQWyG82R1.jpg, 2025-05-24 20:55:03, 2025-05-24 20:55:03)) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\app\\Http\\Controllers\\LivreController.php(41): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\LivreController->store()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#63 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#64 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'auteur_id' doesn't have a default value at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3785): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\app\\Http\\Controllers\\LivreController.php(41): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\LivreController->store()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#58 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#60 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#65 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#66 {main}
"} 
[2025-05-24 22:08:50] local.ERROR: syntax error, unexpected end of file, expecting "elseif" or "else" or "endif" (View: C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views\auth\login.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" (View: C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\resources\\views\\auth\\login.blade.php) at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\08e944de3806d8454b1c5cd180c4f3ff.php:162)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#57 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\storage\\framework\\views\\08e944de3806d8454b1c5cd180c4f3ff.php:162)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\RedirectIfAuthenticated->handle()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#58 {main}
"} 
[2025-05-24 22:24:44] local.INFO: Utilisateur connecté {"user_id":1,"email":"<EMAIL>","ip":"127.0.0.1"} 
[2025-05-24 22:24:45] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/livre","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:24:45"} 
[2025-05-24 22:24:46] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":329.91,"content_type":"text/html; charset=UTF-8","content_length":15447} 
[2025-05-24 22:24:51] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/livre","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:24:51"} 
[2025-05-24 22:24:51] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":64.84,"content_type":"text/html; charset=UTF-8","content_length":15350} 
[2025-05-24 22:25:01] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/livre?page=6","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:25:01"} 
[2025-05-24 22:25:01] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":44.89,"content_type":"text/html; charset=UTF-8","content_length":13186} 
[2025-05-24 22:25:03] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/livre?page=1","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:25:03"} 
[2025-05-24 22:25:03] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":44.98,"content_type":"text/html; charset=UTF-8","content_length":15350} 
[2025-05-24 22:25:07] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/test-middleware","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:25:07"} 
[2025-05-24 22:25:07] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":109.99,"content_type":"text/html; charset=UTF-8","content_length":11790} 
[2025-05-24 22:26:06] local.INFO: 🔍 TestMiddleware - Requête entrante {"url":"http://127.0.0.1:8000/demo-middleware","method":"GET","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","timestamp":"2025-05-24 22:26:06"} 
[2025-05-24 22:26:06] local.INFO: 📤 TestMiddleware - Réponse sortante {"status_code":200,"processing_time_ms":156.42,"content_type":"application/json","content_length":144} 
[2025-05-24 22:26:17] local.INFO: Utilisateur déconnecté {"user_id":1,"email":"<EMAIL>","ip":"127.0.0.1"} 
[2025-05-25 11:30:15] local.ERROR: Maximum execution time of 30 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\Users\\<USER>\\OneDrive\\Desktop\\laravel-exercices\\regionale\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:19)
[stacktrace]
#0 {main}
"} 
[2025-05-25 11:31:05] local.INFO: Utilisateur connecté {"user_id":1,"email":"<EMAIL>","ip":"127.0.0.1"} 
