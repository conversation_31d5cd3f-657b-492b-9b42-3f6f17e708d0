@extends("layout")

@section("content")
<div class="container mt-5">
    <h1 class="mb-4">Modifier un livre</h1>

    <form action="{{ route('livre.update', $livre->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method("PUT")

        <div class="mb-3">
            <label for="titre" class="form-label">Titre</label>
            <input type="text" name="titre" id="titre" class="form-control" value="{{ old('titre', $livre->titre) }}">
            @error('titre')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="auteur_id" class="form-label">Auteur</label>
            <select name="auteur_id" id="auteur_id" class="form-control">
                <option value="">-- Choisir un auteur --</option>
                @foreach($auteurs as $auteur)
                    <option value="{{ $auteur->id }}"
                        {{ old('auteur_id', $livre->auteur_id) == $auteur->id ? 'selected' : '' }}>
                        {{ $auteur->nom }} {{ $auteur->prenom ?? '' }}
                    </option>
                @endforeach
            </select>
            @error('auteur_id')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="image" class="form-label">Image</label>
            <input type="file" name="image" id="image" class="form-control">
            @if($livre->image)
                <div class="mt-2">
                    <img src="{{ asset('storage/' . $livre->image) }}" alt="Image actuelle" width="100">
                    <small class="text-muted d-block">Image actuelle</small>
                </div>
            @endif
            @error('image')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" class="btn btn-primary">Modifier</button>
        <a href="{{ route('livre.index') }}" class="btn btn-secondary">Annuler</a>
    </form>
</div>
@endsection