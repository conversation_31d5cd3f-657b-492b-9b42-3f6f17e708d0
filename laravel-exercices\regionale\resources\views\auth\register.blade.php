@extends("layout")

@section("content")
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">✨ Inscription</h4>
                </div>
                <div class="card-body">
                    {{-- Explication de l'inscription --}}
                    <div class="alert alert-info">
                        <strong>ℹ️ Processus d'inscription :</strong><br>
                        1. Remplissez le formulaire avec vos informations<br>
                        2. Laravel valide et sécurise vos données<br>
                        3. Votre mot de passe est haché (crypté) en base<br>
                        4. Vous êtes automatiquement connecté après inscription
                    </div>

                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        {{-- Nom --}}
                        <div class="mb-3">
                            <label for="name" class="form-label">👤 Nom complet</label>
                            <input type="text" 
                                   name="name" 
                                   id="name" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   value="{{ old('name') }}" 
                                   placeholder="Votre nom complet"
                                   required>
                            @error('name')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        {{-- Email --}}
                        <div class="mb-3">
                            <label for="email" class="form-label">📧 Email</label>
                            <input type="email" 
                                   name="email" 
                                   id="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   value="{{ old('email') }}" 
                                   placeholder="<EMAIL>"
                                   required>
                            @error('email')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                            <small class="form-text text-muted">
                                Votre email doit être unique et valide
                            </small>
                        </div>

                        {{-- Mot de passe --}}
                        <div class="mb-3">
                            <label for="password" class="form-label">🔑 Mot de passe</label>
                            <input type="password" 
                                   name="password" 
                                   id="password" 
                                   class="form-control @error('password') is-invalid @enderror" 
                                   placeholder="Minimum 6 caractères"
                                   required>
                            @error('password')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                            <small class="form-text text-muted">
                                Minimum 6 caractères pour la sécurité
                            </small>
                        </div>

                        {{-- Confirmation du mot de passe --}}
                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">🔐 Confirmer le mot de passe</label>
                            <input type="password" 
                                   name="password_confirmation" 
                                   id="password_confirmation" 
                                   class="form-control" 
                                   placeholder="Répétez votre mot de passe"
                                   required>
                            <small class="form-text text-muted">
                                Doit être identique au mot de passe ci-dessus
                            </small>
                        </div>

                        {{-- Bouton d'inscription --}}
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                🎉 Créer mon compte
                            </button>
                        </div>
                    </form>

                    {{-- Lien vers la connexion --}}
                    <div class="text-center mt-3">
                        <p class="mb-0">Déjà un compte ?</p>
                        <a href="{{ route('login') }}" class="btn btn-link">
                            🔐 Se connecter
                        </a>
                    </div>

                    {{-- Informations de sécurité --}}
                    <div class="alert alert-success mt-3">
                        <strong>🔒 Sécurité :</strong><br>
                        • Votre mot de passe est crypté avec bcrypt<br>
                        • Vos données sont protégées par HTTPS<br>
                        • Session sécurisée avec token CSRF
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Explication technique en commentaire HTML --}}
<!--
🔧 EXPLICATION TECHNIQUE DE L'INSCRIPTION :

1. VALIDATION :
   - name: required|string|max:255
   - email: required|email|unique:users
   - password: required|min:6|confirmed

2. SÉCURITÉ :
   - Hash::make() : Hache le mot de passe avec bcrypt
   - unique:users : Vérifie l'unicité de l'email
   - confirmed : Vérifie que password === password_confirmation

3. PROCESSUS :
   - Validation des données
   - Création de l'utilisateur en base
   - Connexion automatique avec Auth::login()
   - Redirection vers l'application

4. PROTECTION :
   - Token CSRF pour éviter les attaques
   - Validation côté serveur
   - Hashage sécurisé du mot de passe
-->
@endsection
