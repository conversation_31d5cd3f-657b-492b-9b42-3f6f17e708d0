<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LivreController;
use App\Http\Controllers\AuthController;

/*
|--------------------------------------------------------------------------
| EXPLICATION DES ROUTES ET MIDDLEWARES
|--------------------------------------------------------------------------
|
| Les routes définissent les URLs de votre application et les actions à exécuter.
| Les middlewares sont des filtres qui s'exécutent avant ou après les routes.
|
| TYPES DE MIDDLEWARES :
| - auth : Vérifie que l'utilisateur est connecté
| - guest : Vérifie que l'utilisateur n'est PAS connecté
| - test : Notre middleware personnalisé de démonstration
|
| GROUPES DE ROUTES :
| - Route::middleware() : Applique un middleware à plusieurs routes
| - Route::group() : Groupe plusieurs routes avec des propriétés communes
|
*/

// ========================================
// ROUTES PUBLIQUES (sans authentification)
// ========================================

// Page d'accueil - accessible à tous
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Page de debug de l'authentification - accessible à tous
Route::get('/debug-auth', function () {
    return view('debug-auth');
})->name('debug.auth');

// ========================================
// ROUTES D'AUTHENTIFICATION
// ========================================

// Routes pour les utilisateurs NON connectés (middleware 'guest')
Route::middleware('guest')->group(function () {
    // Afficher le formulaire de connexion
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');

    // Traiter la connexion
    Route::post('/login', [AuthController::class, 'login']);

    // Afficher le formulaire d'inscription
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');

    // Traiter l'inscription
    Route::post('/register', [AuthController::class, 'register']);
});

// Route de déconnexion (pour les utilisateurs connectés)
Route::post('/logout', [AuthController::class, 'logout'])
    ->middleware('auth')
    ->name('logout');

// ========================================
// ROUTES PROTÉGÉES (avec authentification + middleware de test)
// ========================================

/*
 * EXPLICATION DU GROUPE DE ROUTES PROTÉGÉES :
 *
 * middleware(['auth', 'test']) signifie :
 * 1. 'auth' : L'utilisateur DOIT être connecté
 * 2. 'test' : Notre middleware personnalisé s'exécute aussi
 *
 * Si l'utilisateur n'est pas connecté :
 * - Il est redirigé vers /login automatiquement
 * - Laravel sauvegarde l'URL demandée pour rediriger après connexion
 */
Route::middleware(['auth', 'test'])->group(function () {
    // Routes des livres - TOUTES protégées par l'authentification
    Route::resource('/livre', LivreController::class);

    // Route de test pour démontrer le middleware
    Route::get('/test-middleware', function () {
        return view('test-middleware');
    })->name('test.middleware');

    // Route de test pour l'affichage des images
    Route::get('/test-images', function () {
        return view('test-images');
    })->name('test.images');
});

// ========================================
// ROUTE DE DÉMONSTRATION DU MIDDLEWARE SEUL
// ========================================

// Cette route utilise SEULEMENT notre middleware de test (pas d'auth)
Route::get('/demo-middleware', function () {
    return response()->json([
        'message' => 'Cette route utilise seulement le TestMiddleware',
        'user' => auth()->user() ? 'Connecté: ' . auth()->user()->name : 'Non connecté',
        'timestamp' => now()->toISOString()
    ]);
})->middleware('test')->name('demo.middleware');