<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Livre;

class LivreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('livres')->insert([
            'titre'=>Str::random(10),
            'image'=>Str::random(10),
            'auteur_id'=>rand(1,3)
        ]);
    Livre::factory(10)->create();


    }
}
