<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Livre;

class LivreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer quelques livres manuellement
        DB::table('livres')->insert([
            [
                'titre' => 'Le Petit Prince',
                'image' => 'images/petit-prince.jpg',
                'auteur_id' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'titre' => 'Les Misérables',
                'image' => 'images/miserables.jpg',
                'auteur_id' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'titre' => 'L\'Étranger',
                'image' => 'images/etranger.jpg',
                'auteur_id' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);

        // Créer 25 livres avec la factory pour tester la pagination
        Livre::factory(25)->create();
    }
}
