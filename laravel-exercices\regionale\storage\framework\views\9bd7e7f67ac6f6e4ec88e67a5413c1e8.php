<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Livres - Application avec Authentification</title>

        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
            integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous">
        </script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    </head>

    <body>
        
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                    📚 Bibliothèque
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <?php if(auth()->guard()->check()): ?>
                            
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('livre.index')); ?>">
                                    📖 Livres
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('test.middleware')); ?>">
                                    🧪 Test Middleware
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('test.images')); ?>">
                                    🖼️ Test Images
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>

                    <ul class="navbar-nav">
                        <?php if(auth()->guard()->guest()): ?>
                            
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('login')); ?>">
                                    🔐 Connexion
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('register')); ?>">
                                    ✨ Inscription
                                </a>
                            </li>
                        <?php else: ?>
                            
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    👤 <?php echo e(Auth::user()->name); ?>

                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <span class="dropdown-item-text">
                                            <small class="text-muted"><?php echo e(Auth::user()->email); ?></small>
                                        </span>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="dropdown-item">
                                                🚪 Déconnexion
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>

        
        <main>
            <?php echo $__env->yieldContent("content"); ?>
        </main>

        
        <footer class="bg-light mt-5 py-3">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            📚 Application de gestion de livres avec authentification
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if(auth()->guard()->check()): ?>
                            <small class="text-muted">
                                Connecté en tant que <strong><?php echo e(Auth::user()->name); ?></strong>
                            </small>
                        <?php else: ?>
                            <small class="text-muted">
                                <a href="<?php echo e(route('login')); ?>">Se connecter</a> pour accéder à l'application
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </footer>
    </body>

</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/layout.blade.php ENDPATH**/ ?>