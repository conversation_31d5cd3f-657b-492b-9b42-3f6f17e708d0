@extends("layout")

@section("content")
<div class="container mt-4">
    <h1 class="mb-4">Liste des livres</h1>

    <a href="{{ route('livre.create') }}" class="btn btn-primary mb-3">Ajouter un livre</a>
      @if(session('success'))
                                    <div class="alert alert-success">
                                        {{ session('success') }}
                                    </div>
                                @endif


    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>Image</th>
                <th>Titre</th>
                <th>Auteur</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($livres as $livre)
                <tr>
                    <td><img src="{{ asset('storage/' . $livre->image) }}" alt="Image du livre" width="100"></td>
                    <td>{{ $livre->titre }}</td>
                    <td>{{ $livre->auteur->nom ?? 'N/A' }} {{ $livre->auteur->prenom ?? '' }}</td>
                    <td>
                        <a href="{{ route('livre.show', $livre->id) }}" class="btn btn-info btn-sm">Afficher</a>
                        <a href="{{ route('livre.edit', $livre->id) }}" class="btn btn-warning btn-sm">Modifier</a>

                       <form action="{{ route('livre.destroy', $livre->id) }}" method="post" onsubmit="return confirm('Voulez-vous vraiment supprimer ce livre ?')" style="display:inline-block;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">Supprimer</button>
                            </form>

                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">Aucun livre trouvé.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
@endsection
