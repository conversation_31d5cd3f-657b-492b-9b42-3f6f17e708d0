@extends("layout")

@section("content")
<div class="container mt-4">
    <h1 class="mb-4">Liste des livres</h1>

    <div class="d-flex justify-content-between align-items-center mb-3">
        <a href="{{ route('livre.create') }}" class="btn btn-primary">Ajouter un livre</a>

        <div class="d-flex align-items-center gap-3">
            <!-- Sélecteur du nombre d'éléments par page -->
            <div class="d-flex align-items-center">
                <label for="per_page" class="form-label me-2 mb-0">Afficher :</label>
                <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                    <option value="5" {{ request('per_page', 5) == 5 ? 'selected' : '' }}>5</option>
                    <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                    <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                    <option value="20" {{ request('per_page') == 20 ? 'selected' : '' }}>20</option>
                </select>
                <span class="ms-2">par page</span>
            </div>

            <div class="text-muted">
                <small>
                    Affichage de {{ $livres->firstItem() ?? 0 }} à {{ $livres->lastItem() ?? 0 }}
                    sur {{ $livres->total() }} livres
                </small>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif


    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>Image</th>
                <th>Titre</th>
                <th>Auteur</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($livres as $livre)
                <tr>
                    <td><img src="{{ asset('storage/' . $livre->image) }}" alt="Image du livre" width="100"></td>
                    <td>{{ $livre->titre }}</td>
                    <td>{{ $livre->auteur->nom ?? 'N/A' }} {{ $livre->auteur->prenom ?? '' }}</td>
                    <td>
                        <a href="{{ route('livre.show', $livre->id) }}" class="btn btn-info btn-sm">Afficher</a>
                        <a href="{{ route('livre.edit', $livre->id) }}" class="btn btn-warning btn-sm">Modifier</a>

                       <form action="{{ route('livre.destroy', $livre->id) }}" method="post" onsubmit="return confirm('Voulez-vous vraiment supprimer ce livre ?')" style="display:inline-block;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">Supprimer</button>
                            </form>

                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="4" class="text-center">Aucun livre trouvé.</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Liens de pagination -->
    <div class="d-flex justify-content-center mt-4">
        {{ $livres->links() }}
    </div>
</div>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Retourner à la première page
    window.location.href = url.toString();
}
</script>

@endsection
