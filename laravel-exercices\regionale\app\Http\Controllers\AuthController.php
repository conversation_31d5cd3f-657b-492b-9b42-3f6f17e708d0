<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

/**
 * Contrôleur d'authentification personnalisé
 *
 * EXPLICATION DE L'AUTHENTIFICATION :
 *
 * L'authentification est le processus de vérification de l'identité d'un utilisateur.
 * Elle comprend généralement :
 * - L'inscription (création d'un compte)
 * - La connexion (vérification des identifiants)
 * - La déconnexion (fin de session)
 * - La gestion des sessions
 */
class AuthController extends Controller
{
    /**
     * Afficher le formulaire de connexion
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Traiter la connexion
     *
     * PROCESSUS DE CONNEXION :
     * 1. Valider les données (email + mot de passe)
     * 2. Tenter l'authentification avec Auth::attempt()
     * 3. Si succès : créer une session et rediriger
     * 4. Si échec : retourner avec erreur
     */
    public function login(Request $request)
    {
        // 1. Validation des données
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6',
        ], [
            'email.required' => 'L\'email est obligatoire',
            'email.email' => 'L\'email doit être valide',
            'password.required' => 'Le mot de passe est obligatoire',
            'password.min' => 'Le mot de passe doit contenir au moins 6 caractères',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // 2. Tentative d'authentification
        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember'); // Case "Se souvenir de moi"

        if (Auth::attempt($credentials, $remember)) {
            // 3. Succès : régénérer la session pour la sécurité
            $request->session()->regenerate();

            // Logger la connexion
            logger()->info('Utilisateur connecté', [
                'user_id' => Auth::id(),
                'email' => Auth::user()->email,
                'ip' => $request->ip()
            ]);

            return redirect()->intended('/livre')->with('success', 'Connexion réussie !');
        }

        // 4. Échec : retourner avec erreur
        return back()->withErrors([
            'email' => 'Ces identifiants ne correspondent à aucun compte.',
        ])->withInput();
    }

    /**
     * Afficher le formulaire d'inscription
     */
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    /**
     * Traiter l'inscription
     *
     * PROCESSUS D'INSCRIPTION :
     * 1. Valider les données
     * 2. Vérifier que l'email n'existe pas déjà
     * 3. Hasher le mot de passe
     * 4. Créer l'utilisateur
     * 5. Connecter automatiquement l'utilisateur
     */
    public function register(Request $request)
    {
        // 1. Validation des données
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ], [
            'name.required' => 'Le nom est obligatoire',
            'email.required' => 'L\'email est obligatoire',
            'email.email' => 'L\'email doit être valide',
            'email.unique' => 'Cet email est déjà utilisé',
            'password.required' => 'Le mot de passe est obligatoire',
            'password.min' => 'Le mot de passe doit contenir au moins 6 caractères',
            'password.confirmed' => 'La confirmation du mot de passe ne correspond pas',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // 2. Créer l'utilisateur
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password), // Hasher le mot de passe
        ]);

        // 3. Connecter automatiquement l'utilisateur
        Auth::login($user);

        // Logger l'inscription
        logger()->info('Nouvel utilisateur inscrit', [
            'user_id' => $user->id,
            'email' => $user->email,
            'ip' => $request->ip()
        ]);

        return redirect('/livre')->with('success', 'Inscription réussie ! Bienvenue !');
    }

    /**
     * Déconnexion
     *
     * PROCESSUS DE DÉCONNEXION :
     * 1. Déconnecter l'utilisateur
     * 2. Invalider la session
     * 3. Régénérer le token CSRF
     * 4. Rediriger vers la page de connexion
     */
    public function logout(Request $request)
    {
        // Logger la déconnexion
        if (Auth::check()) {
            logger()->info('Utilisateur déconnecté', [
                'user_id' => Auth::id(),
                'email' => Auth::user()->email,
                'ip' => $request->ip()
            ]);
        }

        // 1. Déconnecter l'utilisateur
        Auth::logout();

        // 2. Invalider la session
        $request->session()->invalidate();

        // 3. Régénérer le token CSRF
        $request->session()->regenerateToken();

        return redirect('/login')->with('success', 'Déconnexion réussie !');
    }
}
