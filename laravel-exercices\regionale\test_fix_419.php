<?php
/**
 * Script de test pour vérifier la correction de l'erreur 419
 */

echo "🔧 Test de correction de l'erreur 419 - Page Expired\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Test 1: Vérifier la configuration des sessions
echo "1. 📋 Configuration des sessions :\n";

// Lire le fichier .env
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    if (preg_match('/SESSION_DRIVER=(.+)/', $envContent, $matches)) {
        $sessionDriver = trim($matches[1]);
        echo "   ✅ SESSION_DRIVER = {$sessionDriver}\n";
        
        if ($sessionDriver === 'file') {
            echo "   ✅ Configuration correcte : utilisation des sessions fichier\n";
        } else {
            echo "   ⚠️  Driver de session : {$sessionDriver}\n";
        }
    }
} else {
    echo "   ❌ Fichier .env non trouvé\n";
}

echo "\n";

// Test 2: Vérifier le dossier des sessions
echo "2. 📁 Dossier des sessions :\n";
$sessionPath = 'storage/framework/sessions';
if (is_dir($sessionPath)) {
    echo "   ✅ Dossier sessions existe : {$sessionPath}\n";
    
    // Vérifier les permissions
    if (is_writable($sessionPath)) {
        echo "   ✅ Dossier sessions accessible en écriture\n";
    } else {
        echo "   ❌ Dossier sessions non accessible en écriture\n";
    }
    
    // Compter les fichiers de session
    $sessionFiles = glob($sessionPath . '/*');
    $count = count($sessionFiles);
    echo "   📊 Nombre de fichiers de session : {$count}\n";
} else {
    echo "   ❌ Dossier sessions n'existe pas : {$sessionPath}\n";
    echo "   💡 Création du dossier...\n";
    if (mkdir($sessionPath, 0755, true)) {
        echo "   ✅ Dossier créé avec succès\n";
    } else {
        echo "   ❌ Impossible de créer le dossier\n";
    }
}

echo "\n";

// Test 3: Vérifier les fichiers modifiés
echo "3. 🔧 Fichiers modifiés pour corriger l'erreur 419 :\n";

$modifiedFiles = [
    '.env' => 'Configuration SESSION_DRIVER=file',
    'bootstrap/app.php' => 'Gestionnaire d\'exception 419',
    'app/Http/Controllers/AuthController.php' => 'Gestion TokenMismatchException',
    'resources/views/auth/login.blade.php' => 'Affichage messages d\'erreur',
];

foreach ($modifiedFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$file} - {$description}\n";
    } else {
        echo "   ❌ {$file} - MANQUANT\n";
    }
}

echo "\n";

// Test 4: Solutions appliquées
echo "4. 🛠️ Solutions appliquées :\n";
echo "   ✅ Changement du driver de session : database → file\n";
echo "   ✅ Création du dossier storage/framework/sessions\n";
echo "   ✅ Gestionnaire d'exception personnalisé pour erreur 419\n";
echo "   ✅ Gestion des erreurs CSRF dans AuthController\n";
echo "   ✅ Affichage des messages d'erreur dans la vue login\n";
echo "   ✅ Nettoyage du cache de configuration\n";

echo "\n";

// Test 5: Instructions de test
echo "5. 🧪 Comment tester la correction :\n";
echo "   1. Démarrer le serveur : php artisan serve\n";
echo "   2. Aller sur http://127.0.0.1:8000/login\n";
echo "   3. Laisser la page ouverte pendant 2+ heures (ou modifier SESSION_LIFETIME)\n";
echo "   4. Essayer de se connecter → Message d'erreur au lieu de 419\n";
echo "   5. Ou forcer l'erreur en supprimant les cookies de session\n";

echo "\n";

// Test 6: Vérification de la clé APP_KEY
echo "6. 🔑 Vérification de la clé d'application :\n";
if (preg_match('/APP_KEY=(.+)/', file_get_contents('.env'), $matches)) {
    $appKey = trim($matches[1]);
    if (!empty($appKey) && $appKey !== 'base64:') {
        echo "   ✅ APP_KEY configurée correctement\n";
    } else {
        echo "   ❌ APP_KEY manquante ou invalide\n";
        echo "   💡 Exécuter : php artisan key:generate\n";
    }
} else {
    echo "   ❌ APP_KEY non trouvée dans .env\n";
}

echo "\n";

// Test 7: Conseils pour éviter l'erreur 419
echo "7. 💡 Conseils pour éviter l'erreur 419 :\n";
echo "   • Augmenter SESSION_LIFETIME dans .env (défaut: 120 minutes)\n";
echo "   • Utiliser SESSION_DRIVER=file pour plus de stabilité\n";
echo "   • Vérifier que APP_KEY est définie\n";
echo "   • S'assurer que les cookies sont activés dans le navigateur\n";
echo "   • Éviter d'ouvrir plusieurs onglets avec la même session\n";
echo "   • Implémenter une gestion d'erreur gracieuse\n";

echo "\n";
echo "✅ Correction de l'erreur 419 terminée !\n";
echo "🎉 L'authentification devrait maintenant fonctionner correctement.\n";
