@extends("layout")

@section("content")
<div class="container mt-5">
    <h1 class="mb-4">Détail du livre</h1>

    <div class="card" style="width: 18rem;">
        {{-- Affichage de l'image du livre --}}
        <img 
            src="{{ $livre->image && file_exists(public_path('storage/' . $livre->image)) 
                        ? asset('storage/' . $livre->image) 
                        : asset('images/default-book.svg') }}" 
            class="card-img-top"
            alt="Image du livre"
            style="height: 250px; object-fit: cover;"
            onerror="this.src='{{ asset('images/default-book.svg') }}'">

        <div class="card-body">
            <h5 class="card-title">{{ $livre->titre }}</h5>
            <p class="card-text">
                <strong>Auteur :</strong> {{ $livre->auteur->nom }} {{ $livre->auteur->prenom }}
            </p>
            <a href="{{ route('livre.index') }}" class="btn btn-secondary">Retour</a>
        </div>
    </div>
</div>
@endsection
