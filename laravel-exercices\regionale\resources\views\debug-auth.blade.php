@extends("layout")

@section("content")
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">🔍 Debug de l'authentification</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>🧪 Test des directives Blade d'authentification</h5>
                        <p>Cette page permet de diagnostiquer pourquoi les boutons Login/Register ne s'affichent pas.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>📊 État d'authentification :</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>Auth::check() :</strong> 
                                    <span class="badge bg-{{ Auth::check() ? 'success' : 'danger' }}">
                                        {{ Auth::check() ? 'true' : 'false' }}
                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Auth::guest() :</strong> 
                                    <span class="badge bg-{{ Auth::guest() ? 'success' : 'danger' }}">
                                        {{ Auth::guest() ? 'true' : 'false' }}
                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Auth::id() :</strong> 
                                    <span class="badge bg-info">
                                        {{ Auth::id() ?? 'null' }}
                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Session ID :</strong> 
                                    <span class="badge bg-secondary">
                                        {{ session()->getId() }}
                                    </span>
                                </li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h5>👤 Informations utilisateur :</h5>
                            @auth
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Nom :</strong> {{ Auth::user()->name }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Email :</strong> {{ Auth::user()->email }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>ID :</strong> {{ Auth::user()->id }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Créé le :</strong> {{ Auth::user()->created_at }}
                                    </li>
                                </ul>
                            @else
                                <div class="alert alert-warning">
                                    <strong>❌ Aucun utilisateur connecté</strong><br>
                                    Les boutons Login/Register devraient être visibles.
                                </div>
                            @endauth
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🧪 Test des directives Blade :</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header">
                                        <strong>@guest (utilisateurs non connectés)</strong>
                                    </div>
                                    <div class="card-body">
                                        @guest
                                            <div class="alert alert-success">
                                                ✅ @guest fonctionne !<br>
                                                <strong>Les boutons Login/Register devraient être visibles.</strong>
                                            </div>
                                            <div class="btn-group">
                                                <a href="{{ route('login') }}" class="btn btn-primary">
                                                    🔐 Connexion (test)
                                                </a>
                                                <a href="{{ route('register') }}" class="btn btn-success">
                                                    ✨ Inscription (test)
                                                </a>
                                            </div>
                                        @else
                                            <div class="alert alert-info">
                                                ℹ️ @guest ne s'exécute pas car vous êtes connecté.
                                            </div>
                                        @endguest
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header">
                                        <strong>@auth (utilisateurs connectés)</strong>
                                    </div>
                                    <div class="card-body">
                                        @auth
                                            <div class="alert alert-success">
                                                ✅ @auth fonctionne !<br>
                                                <strong>Le menu utilisateur devrait être visible.</strong>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    👤 {{ Auth::user()->name }} (test)
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><span class="dropdown-item-text">{{ Auth::user()->email }}</span></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="POST" action="{{ route('logout') }}">
                                                            @csrf
                                                            <button type="submit" class="dropdown-item">
                                                                🚪 Déconnexion (test)
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        @else
                                            <div class="alert alert-info">
                                                ℹ️ @auth ne s'exécute pas car vous n'êtes pas connecté.
                                            </div>
                                        @endauth
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔧 Informations techniques :</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Driver d'auth :</strong> {{ config('auth.defaults.guard') }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Provider :</strong> {{ config('auth.defaults.provider') }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Driver session :</strong> {{ config('session.driver') }}
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Session lifetime :</strong> {{ config('session.lifetime') }} min
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Cookie name :</strong> {{ config('session.cookie') }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>CSRF token :</strong> 
                                        <small>{{ csrf_token() }}</small>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Route actuelle :</strong> {{ Route::currentRouteName() ?? 'N/A' }}
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Middleware :</strong> 
                                        @if(Route::current())
                                            {{ implode(', ', Route::current()->middleware()) }}
                                        @else
                                            N/A
                                        @endif
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔗 Actions de test :</h5>
                        <div class="btn-group" role="group">
                            @guest
                                <a href="{{ route('login') }}" class="btn btn-primary">
                                    🔐 Aller à la connexion
                                </a>
                                <a href="{{ route('register') }}" class="btn btn-success">
                                    ✨ Aller à l'inscription
                                </a>
                            @else
                                <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                                    @csrf
                                    <button type="submit" class="btn btn-danger">
                                        🚪 Se déconnecter
                                    </button>
                                </form>
                                <a href="{{ route('livre.index') }}" class="btn btn-info">
                                    📚 Voir les livres
                                </a>
                            @endguest
                            <a href="{{ route('home') }}" class="btn btn-secondary">
                                🏠 Accueil
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-4">
                        <h6>💡 Diagnostic :</h6>
                        <ul class="mb-0">
                            <li>Si vous voyez les boutons Login/Register ci-dessus mais pas dans la navigation, le problème vient du layout</li>
                            <li>Si Auth::guest() = true mais @guest ne fonctionne pas, il y a un problème de cache</li>
                            <li>Si la session ID change à chaque rechargement, il y a un problème de session</li>
                            <li>Vérifiez que les routes 'login' et 'register' existent</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
