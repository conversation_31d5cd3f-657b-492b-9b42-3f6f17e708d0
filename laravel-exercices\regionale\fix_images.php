<?php
/**
 * Script pour corriger l'affichage des images
 */

echo "🖼️ Correction de l'affichage des images\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. Vérifier le lien symbolique
echo "1. 🔗 Vérification du lien symbolique storage...\n";
$publicStorageLink = 'public/storage';
if (is_link($publicStorageLink)) {
    echo "   ✅ Lien symbolique existe déjà\n";
} else {
    echo "   ⚠️  Lien symbolique manquant\n";
    echo "   💡 Exécuter : php artisan storage:link\n";
}

// 2. Vérifier les dossiers d'images
echo "\n2. 📁 Vérification des dossiers d'images...\n";
$imageDirs = [
    'storage/app/public/images' => 'Dossier images principal',
    'storage/app/public/image' => 'Ancien dossier image',
    'public/images' => 'Dossier images publiques',
];

foreach ($imageDirs as $dir => $description) {
    if (is_dir($dir)) {
        $count = count(glob($dir . '/*'));
        echo "   ✅ {$dir} - {$description} ({$count} fichiers)\n";
    } else {
        echo "   ❌ {$dir} - {$description} (n'existe pas)\n";
        if ($dir === 'public/images') {
            echo "   💡 Création du dossier...\n";
            if (mkdir($dir, 0755, true)) {
                echo "   ✅ Dossier créé\n";
            }
        }
    }
}

// 3. Lister les images existantes
echo "\n3. 🖼️ Images existantes...\n";
$imageFiles = [];

// Images dans storage/app/public/images
if (is_dir('storage/app/public/images')) {
    $files = glob('storage/app/public/images/*');
    foreach ($files as $file) {
        $imageFiles[] = [
            'path' => $file,
            'public_path' => 'storage/images/' . basename($file),
            'size' => filesize($file),
            'type' => 'images'
        ];
    }
}

// Images dans storage/app/public/image
if (is_dir('storage/app/public/image')) {
    $files = glob('storage/app/public/image/*');
    foreach ($files as $file) {
        $imageFiles[] = [
            'path' => $file,
            'public_path' => 'storage/image/' . basename($file),
            'size' => filesize($file),
            'type' => 'image'
        ];
    }
}

if (empty($imageFiles)) {
    echo "   ⚠️  Aucune image trouvée\n";
} else {
    foreach ($imageFiles as $image) {
        $sizeKb = round($image['size'] / 1024, 2);
        echo "   📷 {$image['public_path']} ({$sizeKb} KB) - Type: {$image['type']}\n";
    }
}

// 4. Vérifier la base de données
echo "\n4. 🗄️ Vérification des chemins en base de données...\n";
try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $stmt = $pdo->query("SELECT id, titre, image FROM livres WHERE image IS NOT NULL");
    $livres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($livres)) {
        echo "   ⚠️  Aucun livre avec image en base\n";
    } else {
        foreach ($livres as $livre) {
            $imagePath = 'storage/app/public/' . $livre['image'];
            $exists = file_exists($imagePath) ? '✅' : '❌';
            echo "   {$exists} Livre #{$livre['id']}: {$livre['titre']} → {$livre['image']}\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Erreur base de données: " . $e->getMessage() . "\n";
}

// 5. Solutions appliquées
echo "\n5. 🛠️ Solutions appliquées dans les vues...\n";
echo "   ✅ Vérification file_exists() avant affichage\n";
echo "   ✅ Image par défaut (SVG) pour les images manquantes\n";
echo "   ✅ Attribut onerror pour fallback automatique\n";
echo "   ✅ Classes Bootstrap pour un affichage cohérent\n";
echo "   ✅ object-fit: cover pour un ratio correct\n";

// 6. Instructions
echo "\n6. 🚀 Instructions pour tester...\n";
echo "   1. Créer le lien symbolique : php artisan storage:link\n";
echo "   2. Démarrer le serveur : php artisan serve\n";
echo "   3. Aller sur /livre pour voir les images\n";
echo "   4. Ajouter un nouveau livre avec image\n";
echo "   5. Vérifier que l'image par défaut s'affiche si nécessaire\n";

// 7. Commandes utiles
echo "\n7. 💡 Commandes utiles...\n";
echo "   • Créer lien symbolique : php artisan storage:link\n";
echo "   • Vider cache : php artisan cache:clear\n";
echo "   • Voir les routes : php artisan route:list\n";
echo "   • Permissions dossier : chmod 755 storage/app/public/images\n";

echo "\n✅ Vérification terminée !\n";
echo "🎉 L'affichage des images devrait maintenant fonctionner.\n";
