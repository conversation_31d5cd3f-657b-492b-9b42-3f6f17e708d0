<?php $__env->startSection("content"); ?>
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">🔍 Debug de l'authentification</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>🧪 Test des directives Blade d'authentification</h5>
                        <p>Cette page permet de diagnostiquer pourquoi les boutons Login/Register ne s'affichent pas.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>📊 État d'authentification :</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>Auth::check() :</strong> 
                                    <span class="badge bg-<?php echo e(Auth::check() ? 'success' : 'danger'); ?>">
                                        <?php echo e(Auth::check() ? 'true' : 'false'); ?>

                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Auth::guest() :</strong> 
                                    <span class="badge bg-<?php echo e(Auth::guest() ? 'success' : 'danger'); ?>">
                                        <?php echo e(Auth::guest() ? 'true' : 'false'); ?>

                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Auth::id() :</strong> 
                                    <span class="badge bg-info">
                                        <?php echo e(Auth::id() ?? 'null'); ?>

                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Session ID :</strong> 
                                    <span class="badge bg-secondary">
                                        <?php echo e(session()->getId()); ?>

                                    </span>
                                </li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h5>👤 Informations utilisateur :</h5>
                            <?php if(auth()->guard()->check()): ?>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Nom :</strong> <?php echo e(Auth::user()->name); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Email :</strong> <?php echo e(Auth::user()->email); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>ID :</strong> <?php echo e(Auth::user()->id); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Créé le :</strong> <?php echo e(Auth::user()->created_at); ?>

                                    </li>
                                </ul>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <strong>❌ Aucun utilisateur connecté</strong><br>
                                    Les boutons Login/Register devraient être visibles.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🧪 Test des directives Blade :</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header">
                                        <strong><?php if(auth()->guard(utilisateurs non connectés)->guest()): ?></strong>
                                    </div>
                                    <div class="card-body">
                                        <?php if(auth()->guard()->guest()): ?>
                                            <div class="alert alert-success">
                                                ✅ <?php if(auth()->guard()->guest()): ?> fonctionne !<br>
                                                <strong>Les boutons Login/Register devraient être visibles.</strong>
                                            </div>
                                            <div class="btn-group">
                                                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                                                    🔐 Connexion (test)
                                                </a>
                                                <a href="<?php echo e(route('register')); ?>" class="btn btn-success">
                                                    ✨ Inscription (test)
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-info">
                                                ℹ️ <?php if(auth()->guard()->guest()): ?> ne s'exécute pas car vous êtes connecté.
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header">
                                        <strong><?php if(auth()->guard(utilisateurs connectés)->check()): ?></strong>
                                    </div>
                                    <div class="card-body">
                                        <?php if(auth()->guard()->check()): ?>
                                            <div class="alert alert-success">
                                                ✅ <?php if(auth()->guard()->check()): ?> fonctionne !<br>
                                                <strong>Le menu utilisateur devrait être visible.</strong>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    👤 <?php echo e(Auth::user()->name); ?> (test)
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><span class="dropdown-item-text"><?php echo e(Auth::user()->email); ?></span></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                                                            <?php echo csrf_field(); ?>
                                                            <button type="submit" class="dropdown-item">
                                                                🚪 Déconnexion (test)
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-info">
                                                ℹ️ <?php if(auth()->guard()->check()): ?> ne s'exécute pas car vous n'êtes pas connecté.
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔧 Informations techniques :</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Driver d'auth :</strong> <?php echo e(config('auth.defaults.guard')); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Provider :</strong> <?php echo e(config('auth.defaults.provider')); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Driver session :</strong> <?php echo e(config('session.driver')); ?>

                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Session lifetime :</strong> <?php echo e(config('session.lifetime')); ?> min
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Cookie name :</strong> <?php echo e(config('session.cookie')); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>CSRF token :</strong> 
                                        <small><?php echo e(csrf_token()); ?></small>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Route actuelle :</strong> <?php echo e(Route::currentRouteName() ?? 'N/A'); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Middleware :</strong> 
                                        <?php if(Route::current()): ?>
                                            <?php echo e(implode(', ', Route::current()->middleware())); ?>

                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔗 Actions de test :</h5>
                        <div class="btn-group" role="group">
                            <?php if(auth()->guard()->guest()): ?>
                                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                                    🔐 Aller à la connexion
                                </a>
                                <a href="<?php echo e(route('register')); ?>" class="btn btn-success">
                                    ✨ Aller à l'inscription
                                </a>
                            <?php else: ?>
                                <form method="POST" action="<?php echo e(route('logout')); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-danger">
                                        🚪 Se déconnecter
                                    </button>
                                </form>
                                <a href="<?php echo e(route('livre.index')); ?>" class="btn btn-info">
                                    📚 Voir les livres
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('home')); ?>" class="btn btn-secondary">
                                🏠 Accueil
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-4">
                        <h6>💡 Diagnostic :</h6>
                        <ul class="mb-0">
                            <li>Si vous voyez les boutons Login/Register ci-dessus mais pas dans la navigation, le problème vient du layout</li>
                            <li>Si Auth::guest() = true mais <?php if(auth()->guard()->guest()): ?> ne fonctionne pas, il y a un problème de cache</li>
                            <li>Si la session ID change à chaque rechargement, il y a un problème de session</li>
                            <li>Vérifiez que les routes 'login' et 'register' existent</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/debug-auth.blade.php ENDPATH**/ ?>