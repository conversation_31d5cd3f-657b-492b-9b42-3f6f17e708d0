<?php $__env->startSection("content"); ?>
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">🖼️ Test d'affichage des images</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>✅ Corrections appliquées :</h5>
                        <ul>
                            <li>✅ Lien symbolique <code>public/storage</code> créé</li>
                            <li>✅ Vérification <code>file_exists()</code> avant affichage</li>
                            <li>✅ Image par défaut SVG pour les images manquantes</li>
                            <li>✅ Attribut <code>onerror</code> pour fallback automatique</li>
                            <li>✅ Classes Bootstrap pour un affichage cohérent</li>
                            <li>✅ <code>object-fit: cover</code> pour un ratio correct</li>
                        </ul>
                    </div>

                    <h5>🧪 Test des images existantes :</h5>
                    <div class="row">
                        
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <img src="<?php echo e(asset('images/default-book.svg')); ?>" 
                                     class="card-img-top" 
                                     alt="Image par défaut"
                                     style="height: 200px; object-fit: cover;">
                                <div class="card-body">
                                    <h6 class="card-title">Image par défaut</h6>
                                    <small class="text-muted">SVG généré automatiquement</small>
                                </div>
                            </div>
                        </div>

                        
                        <?php
                            $imagesDir = public_path('storage/images');
                            $imageFiles = [];
                            if (is_dir($imagesDir)) {
                                $files = glob($imagesDir . '/*');
                                foreach ($files as $file) {
                                    if (in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
                                        $imageFiles[] = 'storage/images/' . basename($file);
                                    }
                                }
                            }
                        ?>

                        <?php $__currentLoopData = $imageFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $imagePath): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="<?php echo e(asset($imagePath)); ?>" 
                                         class="card-img-top" 
                                         alt="Image test"
                                         style="height: 200px; object-fit: cover;"
                                         onerror="this.src='<?php echo e(asset('images/default-book.svg')); ?>'">
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo e(basename($imagePath)); ?></h6>
                                        <small class="text-muted"><?php echo e($imagePath); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        
                        <?php
                            $imageDir = public_path('storage/image');
                            $oldImageFiles = [];
                            if (is_dir($imageDir)) {
                                $files = glob($imageDir . '/*');
                                foreach ($files as $file) {
                                    if (in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
                                        $oldImageFiles[] = 'storage/image/' . basename($file);
                                    }
                                }
                            }
                        ?>

                        <?php $__currentLoopData = $oldImageFiles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $imagePath): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img src="<?php echo e(asset($imagePath)); ?>" 
                                         class="card-img-top" 
                                         alt="Image test (ancien dossier)"
                                         style="height: 200px; object-fit: cover;"
                                         onerror="this.src='<?php echo e(asset('images/default-book.svg')); ?>'">
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo e(basename($imagePath)); ?></h6>
                                        <small class="text-muted"><?php echo e($imagePath); ?> (ancien dossier)</small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <img src="<?php echo e(asset('storage/images/inexistante.jpg')); ?>" 
                                     class="card-img-top" 
                                     alt="Image inexistante"
                                     style="height: 200px; object-fit: cover;"
                                     onerror="this.src='<?php echo e(asset('images/default-book.svg')); ?>'">
                                <div class="card-body">
                                    <h6 class="card-title">Image inexistante</h6>
                                    <small class="text-muted">Test du fallback automatique</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>📊 Informations techniques :</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Lien symbolique :</strong> 
                                        <?php echo e(is_link(public_path('storage')) ? '✅ Actif' : '❌ Manquant'); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Dossier images :</strong> 
                                        <?php echo e(is_dir(public_path('storage/images')) ? '✅ Existe' : '❌ Manquant'); ?>

                                    </li>
                                    <li class="list-group-item">
                                        <strong>Image par défaut :</strong> 
                                        <?php echo e(file_exists(public_path('images/default-book.svg')) ? '✅ Disponible' : '❌ Manquante'); ?>

                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>Images trouvées :</strong> 
                                        <?php echo e(count($imageFiles) + count($oldImageFiles)); ?> fichiers
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Dossier principal :</strong> 
                                        storage/images (<?php echo e(count($imageFiles)); ?> fichiers)
                                    </li>
                                    <li class="list-group-item">
                                        <strong>Ancien dossier :</strong> 
                                        storage/image (<?php echo e(count($oldImageFiles)); ?> fichiers)
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔗 Liens de test :</h5>
                        <div class="btn-group" role="group">
                            <a href="<?php echo e(route('livre.index')); ?>" class="btn btn-primary">
                                📚 Voir les livres
                            </a>
                            <a href="<?php echo e(route('livre.create')); ?>" class="btn btn-success">
                                ➕ Ajouter un livre
                            </a>
                            <a href="<?php echo e(route('test.middleware')); ?>" class="btn btn-info">
                                🧪 Test middleware
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-success mt-4">
                        <h6>💡 Comment ça marche :</h6>
                        <ol>
                            <li><strong>Stockage :</strong> Les images sont stockées dans <code>storage/app/public/images/</code></li>
                            <li><strong>Lien symbolique :</strong> <code>public/storage</code> → <code>storage/app/public</code></li>
                            <li><strong>URL publique :</strong> <code><?php echo e(asset('storage/images/nom-fichier.jpg')); ?></code></li>
                            <li><strong>Vérification :</strong> <code>file_exists(public_path('storage/' . $image))</code></li>
                            <li><strong>Fallback :</strong> Image par défaut si fichier manquant</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("layout", array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel-exercices\regionale\resources\views/test-images.blade.php ENDPATH**/ ?>