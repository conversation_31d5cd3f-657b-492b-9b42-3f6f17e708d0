@extends("layout")

@section("content")
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">🧪 Test du Middleware Personnalisé</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5>✅ Félicitations !</h5>
                        <p>Si vous voyez cette page, cela signifie que :</p>
                        <ul>
                            <li>✅ Vous êtes <strong>authentifié</strong> (middleware 'auth')</li>
                            <li>✅ Le <strong>TestMiddleware</strong> s'est exécuté avec succès</li>
                            <li>✅ Toutes les vérifications de sécurité sont passées</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h5>👤 Informations utilisateur :</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>Nom :</strong> {{ auth()->user()->name }}
                                </li>
                                <li class="list-group-item">
                                    <strong>Email :</strong> {{ auth()->user()->email }}
                                </li>
                                <li class="list-group-item">
                                    <strong>ID :</strong> {{ auth()->user()->id }}
                                </li>
                                <li class="list-group-item">
                                    <strong>Connecté depuis :</strong> 
                                    {{ auth()->user()->created_at->diffForHumans() }}
                                </li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h5>🔧 Informations de la requête :</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>IP :</strong> {{ request()->ip() }}
                                </li>
                                <li class="list-group-item">
                                    <strong>User Agent :</strong> 
                                    <small>{{ request()->userAgent() }}</small>
                                </li>
                                <li class="list-group-item">
                                    <strong>URL :</strong> {{ request()->fullUrl() }}
                                </li>
                                <li class="list-group-item">
                                    <strong>Méthode :</strong> {{ request()->method() }}
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>📊 Tests du middleware :</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">🔍 Logging</h6>
                                        <p class="card-text">Vérifiez les logs dans <code>storage/logs/laravel.log</code></p>
                                        <small class="text-muted">Le middleware log toutes les requêtes</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">⏱️ Performance</h6>
                                        <p class="card-text">Temps de traitement ajouté dans les headers</p>
                                        <small class="text-muted">Ouvrez les DevTools → Network</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">🛡️ Sécurité</h6>
                                        <p class="card-text">Vérifications IP et User-Agent</p>
                                        <small class="text-muted">Protection contre les bots malveillants</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h5>🔗 Liens de test :</h5>
                        <div class="btn-group" role="group">
                            <a href="{{ route('livre.index') }}" class="btn btn-primary">
                                📚 Retour aux livres
                            </a>
                            <a href="{{ route('demo.middleware') }}" class="btn btn-info" target="_blank">
                                🧪 Test middleware seul (JSON)
                            </a>
                            <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-danger">
                                    🚪 Se déconnecter
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6>💡 Comment tester le middleware :</h6>
                        <ol>
                            <li>Ouvrez les <strong>DevTools</strong> de votre navigateur (F12)</li>
                            <li>Allez dans l'onglet <strong>Network</strong></li>
                            <li>Rechargez cette page</li>
                            <li>Cliquez sur la requête pour voir les <strong>headers personnalisés</strong> :
                                <ul>
                                    <li><code>X-Processing-Time</code> : Temps de traitement</li>
                                    <li><code>X-Processed-By</code> : TestMiddleware</li>
                                    <li><code>X-Server-Time</code> : Heure du serveur</li>
                                </ul>
                            </li>
                            <li>Vérifiez les <strong>logs</strong> dans <code>storage/logs/laravel.log</code></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Le middleware ajoute automatiquement des informations de debug ici --}}
@endsection
