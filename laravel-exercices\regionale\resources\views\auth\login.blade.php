@extends("layout")

@section("content")
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">🔐 Connexion</h4>
                </div>
                <div class="card-body">
                    {{-- Affichage des messages de succès --}}
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    {{-- Explication de l'authentification --}}
                    <div class="alert alert-info">
                        <strong>ℹ️ Comment fonctionne l'authentification :</strong><br>
                        1. Saisissez votre email et mot de passe<br>
                        2. Laravel vérifie vos identifiants dans la base de données<br>
                        3. Si correct, une session est créée pour vous identifier<br>
                        4. Vous êtes redirigé vers l'application protégée
                    </div>

                    <form method="POST" action="{{ route('login') }}">
                        @csrf

                        {{-- Email --}}
                        <div class="mb-3">
                            <label for="email" class="form-label">📧 Email</label>
                            <input type="email"
                                   name="email"
                                   id="email"
                                   class="form-control @error('email') is-invalid @enderror"
                                   value="{{ old('email') }}"
                                   placeholder="<EMAIL>"
                                   required>
                            @error('email')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        {{-- Mot de passe --}}
                        <div class="mb-3">
                            <label for="password" class="form-label">🔑 Mot de passe</label>
                            <input type="password"
                                   name="password"
                                   id="password"
                                   class="form-control @error('password') is-invalid @enderror"
                                   placeholder="Votre mot de passe"
                                   required>
                            @error('password')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        {{-- Se souvenir de moi --}}
                        <div class="mb-3 form-check">
                            <input type="checkbox" name="remember" id="remember" class="form-check-input">
                            <label class="form-check-label" for="remember">
                                💾 Se souvenir de moi
                            </label>
                            <small class="form-text text-muted d-block">
                                Garde votre session active plus longtemps
                            </small>
                        </div>

                        {{-- Bouton de connexion --}}
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                🚀 Se connecter
                            </button>
                        </div>
                    </form>

                    {{-- Lien vers l'inscription --}}
                    <div class="text-center mt-3">
                        <p class="mb-0">Pas encore de compte ?</p>
                        <a href="{{ route('register') }}" class="btn btn-link">
                            ✨ Créer un compte
                        </a>
                    </div>

                    {{-- Compte de test --}}
                    <div class="alert alert-warning mt-3">
                        <strong>🧪 Compte de test :</strong><br>
                        Email: <code><EMAIL></code><br>
                        Mot de passe: <code>password</code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{--
🔧 EXPLICATION TECHNIQUE DE LA CONNEXION :

1. FORMULAIRE :
   - method="POST" : Envoie les données de manière sécurisée
   - csrf : Token de protection contre les attaques CSRF
   - action route : Envoie vers AuthController@login

2. VALIDATION :
   - error directive : Affiche les erreurs de validation
   - old helper : Conserve les valeurs en cas d'erreur
   - is-invalid : Classe Bootstrap pour les champs en erreur

3. SÉCURITÉ :
   - type="password" : Masque la saisie
   - Hash::make() : Hache le mot de passe en base
   - Auth::attempt() : Vérifie les identifiants de manière sécurisée

4. SESSION :
   - session()->regenerate() : Régénère l'ID de session
   - remember : Cookie de longue durée
   - Auth::check() : Vérifie si l'utilisateur est connecté
--}}
@endsection
