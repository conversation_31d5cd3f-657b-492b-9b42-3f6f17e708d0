<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Créer un utilisateur de test avec mot de passe connu
        User::factory()->create([
            'name' => 'Utilisateur Test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'), // Mot de passe : "password"
        ]);

        // Créer quelques utilisateurs supplémentaires
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('admin123'),
        ]);

        User::factory(5)->create(); // 5 utilisateurs aléatoires

        $this->call([
            AuteurSeeder::class,
            LivreSeeder::class,
        ]);
    }
}
