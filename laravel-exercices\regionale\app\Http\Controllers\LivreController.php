<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Livre;
use App\Models\Auteur;
use Illuminate\Support\Facades\Storage;

class LivreController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $livres = Livre::with('auteur')->get();
        return view('livre.index', compact('livres'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $auteurs = Auteur::all();
        return view('livre.create' , compact('auteurs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validation des données
        $request->validate([
            'titre' => 'required|unique:livres',
            'auteur_id' => 'required|exists:auteurs,id',
            'image' => 'required|mimes:jpg,jpeg,png'
        ]);

        // Stockage de l'image
        $path = $request->image->store('images', 'public');

        // Création du livre
        Livre::create([
            'titre' => $request->titre,
            'auteur_id' => $request->auteur_id,
            'image' => $path
        ]);

        return redirect()->route('livre.index')->with('success', 'Livre ajouté avec succès');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $livre = Livre::with('auteur')->findOrFail($id);
        return view('livre.show', compact('livre'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $livre = Livre::findOrFail($id);
        $auteurs = Auteur::all();
        return view('livre.edit', compact('livre', 'auteurs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $livre = Livre::findOrFail($id);

        // Validation des données
        $request->validate([
            'titre' => "required|unique:livres,titre,{$livre->id}",
            'auteur_id' => 'required|exists:auteurs,id',
            'image' => 'nullable|mimes:jpg,jpeg,png'
        ]);

        // Préparation des données
        $data = [
            'titre' => $request->titre,
            'auteur_id' => $request->auteur_id
        ];

        // Gestion de l'image si une nouvelle est uploadée
        if ($request->hasFile('image')) {
            // Supprimer l'ancienne image
            if ($livre->image) {
                Storage::disk('public')->delete($livre->image);
            }
            // Stocker la nouvelle image
            $data['image'] = $request->image->store('images', 'public');
        }

        $livre->update($data);
        return redirect()->route('livre.index')->with('success', 'Livre modifié avec succès');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $livre = Livre::findOrFail($id);

        // Supprimer l'image associée
        if ($livre->image) {
            Storage::disk('public')->delete($livre->image);
        }

        $livre->delete();
        return redirect()->route('livre.index')->with('success', 'Livre supprimé avec succès');
    }
}
