<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Livre;
use App\Models\Auteur;
use Illuminate\Support\Facades\Storage;

class LivreController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $livres=Livre::all();
        return view('livre.index' , compact('livres'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $auteurs = Auteur::all();
        return view('livre.create' , compact('auteurs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'titre'=>'required|unique:livres',
            'auteur'=>'required',
            'image'=>'required|mimes:jpg,jpeg,png'
        ]);
        $path=$request->image->store('image','public');
        $data=$request->all();
        $data['image']=$path;
        Livre::create($data);
        return redirect()->route('livre.index')->with('success','livre ajouté avec succès');
  
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $livre=Livre::find($id);
        return view('livre.show',compact('livre'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $livre=Livre::find($id);
        return view('livre.edit',compact('livre'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $livre=Livre::find($id);
        $request->validate([
            'titre'=>'required|unique:livres,titre,'.$livre->id,
            'auteur'=>'required',
            'image'=>'mimes:jpg,jpeg,png'
        ]);
        $data=$request->all();
        if($request->hasFile('image')){
            Storage::disk('public')->delete($livre->image);
            $path=$request->image->store('image','public');
            $data['image']=$path;
        }
        $livre->update($data);
        return redirect()->route('livre.index')->with('success','livre modifié avec succès');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $livre=Livre::find($id);
        Storage::disk('public')->delete($livre->image);
        $livre->delete();
        return redirect()->route('livre.index')->with('success','livre supprimé avec succès');
    }
}
