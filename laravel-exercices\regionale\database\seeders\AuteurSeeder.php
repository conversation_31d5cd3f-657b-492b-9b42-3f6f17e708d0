<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Auteur;

class AuteurSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
      

        DB::table('auteurs')->insert([
            [
                'nom' => 'nachat',
                'prenom' => 'nouhaila',
                'bio' => 'née le ... ',
            ],
            [
                'nom' => 'jshjks',
                'prenom' => 'nousjjshaila',
                'bio' => 'née le ... ',
            ],
            [
                'nom' => 'autre',
                'prenom' => 'auteur',
                'bio' => 'exemple de bio',
            ],
        ]);

        Auteur::factory(10)->create();
    }
}
